export interface PlacedImage {
	id: string;
	src: string;
	x: number;
	y: number;
	width: number;
	height: number;
	rotation: number;
	isGenerated?: boolean;
	parentGroupId?: string;
	cropX?: number;
	cropY?: number;
	cropWidth?: number;
	cropHeight?: number;
	status?: "uploading" | "generating" | "completed";
	ossUrl?: string; // OSS上传成功后的URL
}

export interface HistoryState {
	images: PlacedImage[];
	selectedIds: string[];
}

export interface GenerationSettings {
	prompt: string;
}

export interface ActiveGeneration {
	imageUrl: string;
	prompt: string;
}

export interface SelectionBox {
	startX: number;
	startY: number;
	endX: number;
	endY: number;
	visible: boolean;
}
